<project
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.curefit</groupId>
        <artifactId>ticketing-system</artifactId>
        <version>2.1.9</version>
    </parent>
    <artifactId>ticket-management-commons</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <java.version>11</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>cf-commons-domain</artifactId>
            <version>${curefit.common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>freshdesk-commons</artifactId>
            <version>1.0.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.12.7.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>