package com.curefit.odin.sprinklr.pojo.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrMessage {
    SprinklrMessageContent content;
    String channelMessageId;
    Long channelCreatedTime;
    String language;
    String messageId;
    Boolean brandPost; // true or false denoting agent or customer reply, respectively.
    String conversationId;
    String parentMessageId;
    Boolean autoImported;
    Boolean autoResponse;
    SprinklrMessageProfile senderProfile;
    SprinklrMessageProfile receiverProfile;
}
