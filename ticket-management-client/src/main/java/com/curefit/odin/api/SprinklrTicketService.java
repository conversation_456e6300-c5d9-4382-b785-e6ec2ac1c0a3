package com.curefit.odin.api;

import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketEntry;
import com.curefit.odin.sprinklr.pojo.message.SprinklrMessageCreateRequest;
import com.curefit.odin.sprinklr.pojo.message.SprinklrMessageRequest;
import com.curefit.odin.sprinklr.pojo.message.SprinklrMessageResponse;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrCommentWebhookRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrTicketWebhookRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import static com.curefit.odin.commons.Constants.CUSTOMER_SUPPORT_NAMESPACE;
import static com.curefit.odin.commons.Constants.SPRINKLR_CUSTOMER_SUPPORT_USER;

/**
 * <AUTHOR>
 */

@Component("sprinklrTicketServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrTicketService {

    @Autowired
    CommonHttpHelper commonHttpHelper;

    @Autowired
    OdinConfig odinConfig;

    public void processSprinklrWebhook(SprinklrTicketWebhookRequest sprinklrTicketWebhookRequest) {
        String url = odinConfig.getBaseUri().path("/sprinklr-ticket/webhook")
                .build()
                .toUriString();
        String user = odinConfig.getUser();
        commonHttpHelper.request(url, HttpMethod.POST, sprinklrTicketWebhookRequest, CommonUtils.getHeaders(user), Object.class);
    }

    public PagedResultEntry<Long, SprinklrTicketEntry> searchSprinklrTickets(Long offset, Long limit, String sortBy, String sortOrder, String query) {
        String url = odinConfig.getBaseUri().path("/sprinklr-ticket/searchActive")
                .queryParam("offset", offset)
                .queryParam("limit", limit)
                .queryParam("sortBy", sortBy)
                .queryParam("sortOrder", sortOrder)
                .queryParam("query", query)
                .build()
                .toUriString();
        String user = odinConfig.getUser();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), new TypeReference<PagedResultEntry<Long, SprinklrTicketEntry>>() {
        }).getBody();
    }

    public void addMessage(SprinklrMessageCreateRequest sprinklrMessageCreateRequest) {
        String url = odinConfig.getBaseUri().path("/sprinklr-ticket/add-message")
                .build()
                .toUriString();
        String user = odinConfig.getUser();
        commonHttpHelper.request(url, HttpMethod.POST, sprinklrMessageCreateRequest, CommonUtils.getHeaders(user), Object.class);
    }

    public void processSprinklrCommentWebhook(SprinklrCommentWebhookRequest sprinklrCommentWebhookRequest) {
        String url = odinConfig.getBaseUri().path("/sprinklr-ticket/comment-webhook")
                .build()
                .toUriString();
        commonHttpHelper.request(url, HttpMethod.POST, sprinklrCommentWebhookRequest, CommonUtils.getHeaders(SPRINKLR_CUSTOMER_SUPPORT_USER, CUSTOMER_SUPPORT_NAMESPACE), Object.class);
    }

    public void reOpenTicket(SprinklrMessageCreateRequest request) {
        String url = odinConfig.getBaseUri().path("/sprinklr-ticket/reopen")
                .build()
                .toUriString();
        String user = odinConfig.getUser();
        commonHttpHelper.request(url, HttpMethod.POST, request, CommonUtils.getHeaders(user), Object.class);
    }

    public SprinklrMessageResponse getMessageConversations(SprinklrMessageRequest request) {
        String url = odinConfig.getBaseUri().path("/sprinklr-ticket/fetch-message-conversations")
                .build()
                .toUriString();
        String user = odinConfig.getUser();
        return commonHttpHelper.request(url, HttpMethod.POST, request, CommonUtils.getHeaders(user), new TypeReference<SprinklrMessageResponse>() {
        }).getBody();
    }
}
