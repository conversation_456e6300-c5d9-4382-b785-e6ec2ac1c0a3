spring.datasource.url=*************************************************************************************************
spring.datasource.username=${DB_USER}
spring.datasource.password=${DB_PWD}
spring.jpa.hibernate.ddl-auto=create
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG

logfile.path=/logs/ticketing-system

spring.jackson.time-zone: UTC

spring.jpa.generate-ddl=true
spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=create.sql  
hibernate.hbm2ddl.delimiter=";"
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.ddl-auto=update

#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true
app.environment=stage
rollbar.disabled=false
rollbar.accessToken=e2d30f547dff49cc9b1a4eec6ce187dd
external.vms.baseUrl=http://vms.stage.curefit.co/

spring.cache.redis.key-prefix=ODIN:
spring.cache.type=redis
spring.redis.host=curefit.y66lea.ng.0001.aps1.cache.amazonaws.com
spring.redis.port=6379
spring.redis.database=4
spring.redis.ttl=60

management.metrics.export.prometheus.enabled=true
management.metrics.export.prometheus.descriptions=true
management.metrics.enable.root=true
management.address=127.0.0.1
management.endpoints.jmx.exposure.include=*
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.exclude=
management.endpoints.web.exposure.exclude=

mozart.base.url=http://mozart.stage.cure.fit.internal/v1
neo.url=http://neo.stage.curefit.co:8080
email.enable=true
maverick.url=http://maverick.stage.cure.fit.internal/platform/neo_employee_catalogue
maverick.etl.url=http://maverick.stage.cure.fit.internal/etl/platform/neo_employee_catalogue

freshdesk.url=
notification.default.emails=
iris.baseUrl=http://iris.stage.cure.fit.internal
email.ticket.assign.creative=EMAIL_ODIN_TICKET_ASSIGN
email.ticket.update.creative=EMAIL_ODIN_TICKET_UPDATE
email.ticket.comment.creative=EMAIL_ODIN_TICKET_COMMENT
email.ticket.attachment.creative=EMAIL_ODIN_TICKET_ATTACHMENT
email.ticket.slaReminder.creative=EMAIL_EMAIL_ODIN_TICKET_SLA_REMINDER
email.ticket.slaToBeBreachedTomorrow.slaReminder.creative=EMAIL_EMAIL_ODIN_TICKET_SLA_TO_BE_BREACHED_TOMORROW_SLA_REMINDER
email.ticket.notification.campaign=CUREFIT_ODIN_TICKET_NOTIFICATION

attachment.s3.bucket=cf-platform-odin-stage
email.dl.refresh.cron.expression=0 55 16 ? * *
email.dl.refresh.enable=true
google.oauth.callback.url=https://ticketing-system.stage.curefit.co/google/oauth/callback
google.oauth.redirect.url=http://odin.stage.curefit.co/

freshdesk.baseUrl=https://curefitsandbox.freshdesk.com
freshdesk.apiKey=${FRESHDESK_API_KEY}
freshdesk.source.email=<EMAIL>
odin.base.url=http://odin.stage.curefit.co/
google.location.url=https://maps.googleapis.com/maps/api/geocode/json
google.location.api.key=${GOOGLE_LOC_API_KEY}
email.read.cron.expression=0 0/1 * 1/1 * ?
email.read.enable=true
escalation.events.queue.name=stage-platforms-odin-escalation
escalation.events.queue.batchSize=10
escalation.events.queue.waitTime=1
ticketing-system.employee.update.queue=stage-ticketing-system-employee-update
neo.employee.update.events.queue.batchSize=10
neo.employee.update.events.queue.waitTime=2
mozart.sqs.job.submit.queue=stage-platforms-mozart-job-create
mozart.job.config.id=f6edb8c7-c4e8-41d0-a901-df877e8d69e8
mozart.sla.breach.next.day.job.config.id=ticketing-system-sla-breach-next-day
email.ticket.escalation.creative=EMAIL_ODIN_TICKET_ESCALATION
google.oauth.token.directory=/opt/odin_oauth_tokens
customer.support.email=<EMAIL>
notification.ignored.emails=<EMAIL>
crypto.key=${CRYTO_KEY}
hr.freshdesk.baseUrl=https://curefithr.freshdesk.com
hr.freshdesk.apiKey=${HR_FRESHDESK_API_KEY}
partner.freshdesk.baseUrl=
partner.freshdesk.apiKey=

auth.enabled=false
auth.refreshIntervalMs=300000
auth.enabledUrls=
apiKey=${API_KEY}
K8S_APP_NAME=ticketing-system
audit.scan.package=com.curefit.odin
encryption.scan.package=com.curefit.odin

identity.url=http://identity.stage.cure.fit.internal
watchmen.url=http://watchmen.stage.cure.fit.internal

watchmen.apiKey=odin-stage-access

watchmen.membership.queue.name=stage-odin-watchmen-membership
watchmen.membership.queue.batchSize=10
watchmen.membership.queue.waitTime=1

sugarfit.freshdesk.baseUrl=
sugarfit.freshdesk.apiKey=

odin.email=<EMAIL>

shortloop.enabled=true
shortloop.url=https://k8s-shortloop.stage.curefit.co
shortloop.applicationName=ticketing-system
shortloop.environment=stage

external.neo.baseUrl: http://neo.stage.curefit.co
external.cult-api.baseUrl: http://cultapi.stage.internal.cult.fit

retry.maxAttempts: 3
retry.delay: 2000

gymfit.baseUrl=http://gymfit.stage.cure.fit.internal
gymfit.apiKey=

spring.autoconfigure.exclude= org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration,org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration

spring.flyway.baseline-on-migrate=true
spring.main.allow-circular-references=true

ticketingSystem.baseUrl=http://ticketing-system.stage.cure.fit.internal
ticketingSystem.user=<EMAIL>

services.report-issues.baseUrl=http://localhost:3016
services.report-issues.apiKey=app-stage-access
crypto.cipher.transformation=AES/CTR/NoPadding

iris.requestQueue=stage-iris-campaign
curefit.rashi.redis.port=6379
curefit.rashi.redis.host=platforms-cache.stage.cure.fit.internal
curefit.rashi.redis.clusterEnabled=true
services.rashi.baseUrl=http://rashi.stage.cure.fit.internal
rashi.sns.topicArn=arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events
rashi.event.prefix=TICKETING_SYSTEM_

external.user-service-v1.baseUrl=http://user-service.stage.cure.fit.internal/v1
external.user-service-v2.baseUrl=http://user-service-v2.stage.cure.fit.internal/v1

configstore.enabled=true
configstore.apiKey=ticketing-stage-access
configstore.url=http://config-store.stage.cure.fit.internal/
app.name=ticketing-system

services.falcon.baseUrl: http://falcon.stage.cure.fit.internal
services.falcon.port: 1433
services.falcon.apiKey: ticketing-stage-access