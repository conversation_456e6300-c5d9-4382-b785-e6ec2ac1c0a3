package com.curefit.odin.utils.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.falcon.Filter;
import com.curefit.falcon.FilterType;
import com.curefit.falcon.Location;
import com.curefit.falcon.SelectFilterValue;
import com.curefit.falcon.client.SearchClient;
import com.curefit.falcon.request.SearchRequest;
import com.curefit.falcon.response.SearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class FalconService {

    @Autowired
    SearchClient searchClient;

    public SearchResponse fetchUserByEmailId(String emailId) throws BaseException {
        try{
            return searchClient.search(getSearchRequest(emailId), "odin", UUID.randomUUID().toString(), null, null, false);
        }catch (Exception e) {
            log.error("Unexpected error while fetching user details for {} : {}", emailId, e.getMessage(), e);
            throw new RuntimeException("Unexpected error communicating with Falcon", e);
        }
    }

    public SearchRequest getSearchRequest(String query) {
        SearchRequest searchRequest = new SearchRequest(1, 20);
        searchRequest.setQuery(query);
        searchRequest.setLocations(Collections.singletonList(new Location("IN", "Bangalore")));
        searchRequest.setAppliedFilters(getAppliedFilters());
        return searchRequest;
    }

    public List<Filter> getAppliedFilters() {
        // Creating applied filters with nested structure
        SelectFilterValue userValue = new SelectFilterValue();
        userValue.setTitle("User");
        userValue.setValue("User");
        Filter entityFilter = new Filter(null, "attributes.entityDisplayName", "Entity", FilterType.SELECT, null, null, Collections.singletonList(userValue), null, null);
        SelectFilterValue ticketingSystemValue = new SelectFilterValue(null, "TICKETING_SYSTEM", null, null, Collections.singletonList(entityFilter));
        ticketingSystemValue.setTitle("TICKETING_SYSTEM");
        Filter subVerticalFilter = new Filter(null, "subVertical", "SubVertical", FilterType.SELECT, null, null, Collections.singletonList(ticketingSystemValue), null, null);
        SelectFilterValue odinValue = new SelectFilterValue(null, "ODIN", null, null, Collections.singletonList(subVerticalFilter));
        odinValue.setTitle("ODIN");
        Filter verticalFilter = new Filter(null, "vertical", "Vertical", FilterType.SELECT, null, null, Collections.singletonList(odinValue), null, null);
        return Collections.singletonList(verticalFilter);
    }
}
