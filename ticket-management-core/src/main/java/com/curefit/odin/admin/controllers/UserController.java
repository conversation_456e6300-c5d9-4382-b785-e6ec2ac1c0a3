package com.curefit.odin.admin.controllers;

import java.io.IOException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.User;
import com.curefit.odin.admin.pojo.PagedResultEntryV2;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.service.NeoLocationHandler;

@RestController
@RequestMapping("/user")
public class UserController extends BaseOdinController<User, UserEntry> {

  @Autowired
  private NeoLocationHandler neoLocationHandler;

  public UserController(UserService userService) {
    super(userService);
  }

  @RequestMapping(method = {RequestMethod.POST}, value = {"/maverick"})
  public ResponseEntity addUserAndRefreshMaverick(@RequestBody UserEntry entry) throws BaseException {
    ((UserService) baseMySQLService).addUserAndRefreshMaverick(entry);
    return new ResponseEntity(HttpStatus.NO_CONTENT);
  }

  @RequestMapping(
          method = {RequestMethod.POST},
          value = {"/external"}
  )
  public ResponseEntity<List<UserEntry>> addExternalUsers(@RequestBody List<UserEntry> entries) {
    return new ResponseEntity(((UserService) baseMySQLService).addExternalUsers(entries), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<LocationEntry> getLocationByNamespace(
      @RequestHeader("X_NAMESPACE") String namespace, @RequestHeader("X_USER_ID") String userId)
      throws BaseException {
    return new ResponseEntity<>(neoLocationHandler.getLocation(namespace, userId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, params = {"emailId"})
  public ResponseEntity<UserEntry> findById(@RequestParam String emailId) throws BaseException {
    return new ResponseEntity<>(((UserService) baseMySQLService).findUserByMailId(emailId),
        HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, params = {"query"})
  public ResponseEntity<List<UserEntry>> findUsersByQuery(@RequestParam String query) throws BaseException {
    return new ResponseEntity<>(((UserService) baseMySQLService).findUsersByQuery(query),
            HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = {"/falcon-feature-flag"})
  public ResponseEntity<Boolean> getFalconFeatureFlag() {
    return new ResponseEntity<>(((UserService) baseMySQLService).getFalconFeatureFlag(),
            HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/fetchAll")
  public ResponseEntity<List<UserEntry>> fetchAll() {
    return new ResponseEntity<>(((UserService) baseMySQLService).fetchAll(), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/searchV2")
  public ResponseEntity<PagedResultEntryV2<UserEntry>> searchV2(
      @RequestParam(value = "page", defaultValue = "0") int page,
      @RequestParam(value = "size", defaultValue = "100") int size,
      @RequestParam(value = "sortBy", defaultValue = "NULL") String sortBy,
      @RequestParam(value = "sortOrder", defaultValue = "ASC") String sortOrder,
      @RequestParam(value = "query", required = false) String query)
      throws InvalidSeachQueryException, BaseException {
    ResponseEntity<PagedResultEntry<Long, UserEntry>> result =
        super.search((page * size), size, sortBy, sortOrder, query, null);
    PagedResultEntryV2<UserEntry> res = new PagedResultEntryV2<>(
        result.getBody().getElements(), result.getBody().getTotalElements(),
        result.getBody().getOffset(), result.getBody().getLimit());
    return new ResponseEntity<>(res, HttpStatus.OK);

  }
}
